import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  ManyToMany,
  JoinColumn,
} from 'typeorm';
import { Schedule } from '../../schedules/entities/schedule.entity';
import { Property } from '@/modules/properties/entities/property.entity';
import { OrderClient } from './order-client.entity';
import { OrderInspector } from './order-inspector.entity';
import { User } from '@/modules/users/entities/user.entity';

export enum OrderStatus {
  PENDING = 'pending',
  SCHEDULED = 'scheduled',
  ASSIGNED = 'assigned', //not use
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum PropertyType {
  SINGLE_FAMILY = 'single_family',
  CONDO = 'condo',
  TOWNHOUSE = 'townhouse',
  MULTI_FAMILY = 'multi_family',
  COMMERCIAL = 'commercial',
}

@Entity('inspection_orders')
export class Order {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, length: 50 })
  orderNumber: string;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;



  // Property Information
  @Column({ length: 500 })
  addressLine1: string;

  @Column({ length: 255 })
  city: string;

  @Column({ length: 10 })
  zipCode: string;

  @Column({ length: 50 })
  state: string;

  @Column({
    type: 'enum',
    enum: PropertyType,
    nullable: true,
  })
  propertyType: PropertyType;

  @Column({ nullable: true })
  yearBuilt: number;

  @Column({ nullable: true })
  bathrooms: number;

  @Column({ nullable: true })
  squareFootage: number;

  @Column({ nullable: true })
  bedrooms: number;

  @Column({ nullable: true, length: 50 })
  foundationType: string;

  // Access Information
  @Column({ nullable: true, length: 100 })
  gateCode: string;

  @Column({ nullable: true, length: 100 })
  lockboxCode: string;

  @Column({ nullable: true, length: 100 })
  alarmCode: string;

  @Column({ nullable: true, length: 100 })
  mlsNumber: string;

  @Column({ nullable: true, type: 'text' })
  note: string;

  // Property Conditions
  @Column({ default: false })
  isClientAttending: boolean;

  @Column({ default: false })
  isOccupied: boolean;

  @Column({ default: false })
  hasUtilities: boolean;

  @Column({ default: false })
  hasAlarm: boolean;

  // Services
  @Column({ type: 'jsonb', nullable: true })
  services: {
    flexfund?: boolean;
    mold?: boolean;
    [key: string]: boolean;
  };

  // Agent Information
  @Column({ nullable: true, length: 255 })
  agentName: string;

  @Column({ nullable: true, length: 255 })
  agentEmail: string;

  @Column({ nullable: true, length: 20 })
  agentPhone: string;

  @Column({ default: false })
  isSeller: boolean;

  @Column({ default: false })
  isBuyer: boolean;

  // Pricing
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  inspectionFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  thirdPartyFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  processingFee: number;

  // Scheduling
  @Column({ nullable: true })
  inspectionDate: Date;

  @Column({ nullable: true, type: 'date' })
  scheduledDate: string;

  @Column({ nullable: true, type: 'time' })
  scheduledTime: string;

  // Completion and Cancellation
  @Column({ nullable: true })
  completedAt: Date;

  @Column({ nullable: true })
  cancelledAt: Date;

  @Column({ nullable: true, type: 'text' })
  cancellationReason: string;

  @Column({ nullable: true, type: 'text' })
  inspectionReport: string;

  @Column({ nullable: true, type: 'text' })
  inspectionNotes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Schedule, (schedule) => schedule.order)
  schedules: Schedule[];

  @ManyToOne(() => Property, (property) => property.orders, { nullable: true })
  @JoinColumn({ name: 'propertyId' })
  property: Property;

  // Many-to-Many Relations through Junction Tables
  @OneToMany(() => OrderClient, (orderClient) => orderClient.order, { cascade: true })
  orderClients: OrderClient[];

  @OneToMany(() => OrderInspector, (orderInspector) => orderInspector.order, { cascade: true })
  orderInspectors: OrderInspector[];

  // Relations - Multiple clients support
  @Column({ type: 'int', array: true, nullable: true })
  clientIds: number[];

  // Relations - Multiple inspector support
  @Column({ type: 'int', array: true, nullable: true })
  inspectorIds: number[];
}
