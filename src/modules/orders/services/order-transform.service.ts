import { Injectable } from '@nestjs/common';
import { Order } from '../entities/order.entity';

@Injectable()
export class OrderTransformService {

  /**
   * Get client display name for an order
   */
  getClientDisplayName(order: Order): string {
    if (!order.orderClients || order.orderClients.length === 0) {
      return 'No clients assigned';
    }

    const clientNames = order.orderClients
      .filter(oc => oc.client)
      .map(oc => oc.client.name);

    if (clientNames.length === 0) {
      return 'No clients assigned';
    }

    if (clientNames.length === 1) {
      return clientNames[0];
    }

    return `${clientNames[0]} + ${clientNames.length - 1} others`;
  }

  /**
   * Get inspector display name for an order
   */
  getInspectorDisplayName(order: Order): string {
    if (!order.orderInspectors || order.orderInspectors.length === 0) {
      return 'No inspectors assigned';
    }

    const activeInspectors = order.orderInspectors.filter(oi => oi.isActive);
    const inspectorNames = activeInspectors
      .filter(oi => oi.inspector)
      .map(oi => oi.inspector.name);

    if (inspectorNames.length === 0) {
      return 'No inspectors assigned';
    }

    if (inspectorNames.length === 1) {
      return inspectorNames[0];
    }

    return `${inspectorNames[0]} + ${inspectorNames.length - 1} others`;
  }

  /**
   * Get primary client email for an order
   */
  getPrimaryClientEmail(order: Order): string {
    if (!order.orderClients || order.orderClients.length === 0) {
      return '';
    }

    const primaryClient = order.orderClients.find(oc => oc.isPrimary);
    if (primaryClient && primaryClient.client) {
      return primaryClient.client.email;
    }

    // Fallback to first client
    const firstClient = order.orderClients[0];
    if (firstClient && firstClient.client) {
      return firstClient.client.email;
    }

    return '';
  }

  /**
   * Get primary inspector email for an order
   */
  getPrimaryInspectorEmail(order: Order): string {
    if (!order.orderInspectors || order.orderInspectors.length === 0) {
      return '';
    }

    const activeInspectors = order.orderInspectors.filter(oi => oi.isActive);
    const primaryInspector = activeInspectors.find(oi => oi.role === 'primary');
    
    if (primaryInspector && primaryInspector.inspector) {
      return primaryInspector.inspector.email;
    }

    // Fallback to first inspector
    const firstInspector = activeInspectors[0];
    if (firstInspector && firstInspector.inspector) {
      return firstInspector.inspector.email;
    }

    return '';
  }
}
