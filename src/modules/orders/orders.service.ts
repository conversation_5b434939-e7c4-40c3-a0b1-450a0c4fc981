import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';

import { Order, OrderStatus } from './entities/order.entity';
import { Property } from '../properties/entities/property.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { User, UserRole } from '../users/entities/user.entity';
import { OrderClientService } from './services/order-client.service';
import { OrderInspectorService } from './services/order-inspector.service';
import { OrderTransformService } from './services/order-transform.service';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(Inspector)
    private readonly inspectorRepository: Repository<Inspector>,
    @InjectRepository(Schedule)
    private readonly scheduleRepository: Repository<Schedule>,
    private readonly orderClientService: OrderClientService,
    private readonly orderInspectorService: OrderInspectorService,
  ) {}

  async create(createOrderDto: CreateOrderDto, user: any) {
    // Generate unique order number
    const orderNumber = await this.generateOrderNumber();

    // Validate property if provided
    if (createOrderDto.propertyId) {
      const property = await this.propertyRepository.findOne({
        where: { id: createOrderDto.propertyId },
      });
      if (!property) {
        throw new NotFoundException('Property not found');
      }
    }

    // Validate inspectors if provided
    if (createOrderDto.assignedInspectorIds && createOrderDto.assignedInspectorIds.length > 0) {
      const inspectors = await this.inspectorRepository.find({
        where: { id: In(createOrderDto.assignedInspectorIds), isActive: true },
      });
      if (inspectors.length !== createOrderDto.assignedInspectorIds.length) {
        throw new NotFoundException('One or more inspectors not found or inactive');
      }
    }

    // Handle client IDs - support both single and multiple clients
    let clientIds: number[] = [];
    if (user.role === 'client') {
      // If client is creating order, add their ID
      clientIds = [user.userId];
    } else {
      // If admin is creating order
      if (createOrderDto.clientIds && createOrderDto.clientIds.length > 0) {
        clientIds = createOrderDto.clientIds;
      }
    }

    // Validate clients if provided
    if (clientIds.length > 0) {
      const clients = await this.userRepository.find({
        where: { id: In(clientIds), role: UserRole.CLIENT },
      });
      if (clients.length !== clientIds.length) {
        throw new NotFoundException('One or more clients not found or invalid');
      }
    }

    // Create order without clientIds and assignedInspectorIds
    const { clientIds: _, assignedInspectorIds, ...orderData } = createOrderDto;
    const order = this.orderRepository.create({
      ...orderData,
      orderNumber,
      status: OrderStatus.PENDING,
    });

    const savedOrder = await this.orderRepository.save(order);

    // Add clients to order using junction table
    if (clientIds.length > 0) {
      for (let i = 0; i < clientIds.length; i++) {
        await this.orderClientService.addClientToOrder(
          savedOrder.id,
          clientIds[i],
          i === 0, // First client is primary
        );
      }
    }

    // Add inspectors to order using junction table
    if (assignedInspectorIds && assignedInspectorIds.length > 0) {
      await this.orderInspectorService.replaceOrderInspectors(
        savedOrder.id,
        assignedInspectorIds,
        user.userId,
      );
    }

    return {
      order: await this.findOne(savedOrder.id, user),
      message: 'Order created successfully',
    };
  }

  async findAll(query: OrderQueryDto, user: any) {
    const {
      page = 1,
      limit = 10,
      status,
      clientId,
      inspectorId,
      propertyId,
      search,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    // Include relations
    queryBuilder.leftJoinAndSelect('order.property', 'property');
    queryBuilder.leftJoinAndSelect('order.orderClients', 'orderClients');
    queryBuilder.leftJoinAndSelect('orderClients.client', 'client');
    queryBuilder.leftJoinAndSelect('order.orderInspectors', 'orderInspectors');
    queryBuilder.leftJoinAndSelect('orderInspectors.inspector', 'inspector');

    // Apply filters
    if (status) {
      queryBuilder.andWhere('order.status = :status', { status });
    }

    if (clientId) {
      queryBuilder.andWhere('orderClients.clientId = :clientId', { clientId });
    }

    if (inspectorId) {
      queryBuilder.andWhere('orderInspectors.inspectorId = :inspectorId AND orderInspectors.isActive = true', { inspectorId });
    }

    if (propertyId) {
      queryBuilder.andWhere('order.propertyId = :propertyId', { propertyId });
    }

    if (search) {
      queryBuilder.andWhere(
        '(order.orderNumber ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    // Apply user-based filtering
    if (user.role === 'client') {
      queryBuilder.andWhere('orderClients.clientId = :userId', { userId: user.userId });
    } else if (user.role === 'inspector') {
      queryBuilder.andWhere('orderInspectors.inspectorId = :userId AND orderInspectors.isActive = true', { userId: user.userId });
    }

    // Apply sorting
    queryBuilder.orderBy(`order.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [orders, total] = await queryBuilder.getManyAndCount();

    return {
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number, user: any) {
    const order = await this.orderRepository.findOne({
      where: { id },
      relations: [
        'property',
        'orderClients',
        'orderClients.client',
        'orderInspectors',
        'orderInspectors.inspector',
      ],
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Check permissions
    if (user.role === 'client') {
      const isClientInOrder = await this.orderClientService.isClientInOrder(id, user.userId);
      if (!isClientInOrder) {
        throw new ForbiddenException('You can only view your own orders');
      }
    } else if (user.role === 'inspector') {
      const isInspectorAssigned = await this.orderInspectorService.isInspectorAssignedToOrder(id, user.userId);
      if (!isInspectorAssigned) {
        throw new ForbiddenException('You can only view your assigned orders');
      }
    }

    return order;
  }

  async update(id: number, updateOrderDto: UpdateOrderDto, user: any) {
    const order = await this.findOne(id, user);

    // Check permissions for updates
    if (user.role === 'client') {
      const isClientInOrder = await this.orderClientService.isClientInOrder(id, user.userId);
      if (!isClientInOrder) {
        throw new ForbiddenException('You can only update your own orders');
      }
    }

    // Validate status transitions
    if (updateOrderDto.status) {
      this.validateStatusTransition(order.status, updateOrderDto.status);
    }

    // Handle inspector assignment if provided
    if (updateOrderDto.assignedInspectorIds && updateOrderDto.assignedInspectorIds.length > 0) {
      const inspectors = await this.inspectorRepository.find({
        where: { id: In(updateOrderDto.assignedInspectorIds), isActive: true },
      });
      if (inspectors.length !== updateOrderDto.assignedInspectorIds.length) {
        throw new NotFoundException('One or more inspectors not found or inactive');
      }

      // Use the new service to assign inspectors
      await this.orderInspectorService.replaceOrderInspectors(id, updateOrderDto.assignedInspectorIds, user.userId);
    }

    // Remove assignedInspectorIds from update data since it's handled separately
    const { assignedInspectorIds, ...updateData } = updateOrderDto;
    await this.orderRepository.update(id, updateData);

    const updatedOrder = await this.findOne(id, user);
    return {
      order: updatedOrder,
      message: 'Order updated successfully',
    };
  }

  async remove(id: number, user: any) {
    const order = await this.findOne(id, user);

    // Only allow deletion of pending orders
    if (order.status !== OrderStatus.PENDING) {
      throw new BadRequestException('Only pending orders can be deleted');
    }

    // Check permissions
    if (user.role === 'client') {
      const isClientInOrder = await this.orderClientService.isClientInOrder(id, user.userId);
      if (!isClientInOrder) {
        throw new ForbiddenException('You can only delete your own orders');
      }
    }

    await this.orderRepository.remove(order);

    return {
      message: 'Order deleted successfully',
    };
  }

  async assignInspectors(id: number, inspectorIds: number[], user: any) {
    const order = await this.findOne(id, user);

    if (order.status !== OrderStatus.PENDING) {
      throw new BadRequestException('Can only assign inspectors to pending orders');
    }

    const inspectors = await this.inspectorRepository.find({
      where: { id: In(inspectorIds), isActive: true, isAvailable: true },
    });

    if (inspectors.length !== inspectorIds.length) {
      throw new NotFoundException('One or more inspectors not found or not available');
    }

    // Use the new service to assign inspectors
    await this.orderInspectorService.replaceOrderInspectors(id, inspectorIds, user.userId);

    // Update order status
    await this.orderRepository.update(id, {
      status: OrderStatus.SCHEDULED,
    });

    return {
      message: 'Inspectors assigned successfully',
      order: await this.findOne(id, user),
    };
  }

  async scheduleInspection(id: number, scheduleData: any, user: any) {
    const order = await this.findOne(id, user);

    if (order.status !== OrderStatus.SCHEDULED) {
      throw new BadRequestException('Order must be assigned before scheduling');
    }

    // Find available schedule slot
    const schedule = await this.scheduleRepository.findOne({
      where: {
        id: scheduleData.scheduleId,
        available: true,
      },
    });

    // Check if the schedule belongs to one of the assigned inspectors
    const isInspectorAssigned = await this.orderInspectorService.isInspectorAssignedToOrder(
      order.id,
      schedule.inspectorId,
    );
    if (!isInspectorAssigned) {
      throw new BadRequestException('Schedule does not belong to assigned inspectors');
    }

    if (!schedule) {
      throw new NotFoundException('Schedule slot not available');
    }

    // Update schedule and order
    await this.scheduleRepository.update(scheduleData.scheduleId, {
      inspectionOrderId: id,
      available: false,
    });

    await this.orderRepository.update(id, {
      status: OrderStatus.SCHEDULED,
      scheduledDate: schedule.date,
      scheduledTime: schedule.startTime,
    });

    return {
      message: 'Inspection scheduled successfully',
      order: await this.findOne(id, user),
    };
  }

  async completeInspection(id: number, completionData: any, user: any) {
    const order = await this.findOne(id, user);

    if (order.status !== OrderStatus.IN_PROGRESS) {
      throw new BadRequestException('Order must be in progress to complete');
    }

    // Check if user is the assigned inspector
    if (user.role === 'inspector') {
      const isInspectorAssigned = await this.orderInspectorService.isInspectorAssignedToOrder(id, user.userId);
      if (!isInspectorAssigned) {
        throw new ForbiddenException('You can only complete your assigned inspections');
      }
    }

    await this.orderRepository.update(id, {
      status: OrderStatus.COMPLETED,
      completedAt: new Date(),
      inspectionReport: completionData.report,
      inspectionNotes: completionData.notes,
    });

    // Update inspector's completed inspections count for all assigned inspectors
    const inspectorIds = await this.orderInspectorService.getOrderInspectorIds(id);
    if (inspectorIds.length > 0) {
      for (const inspectorId of inspectorIds) {
        await this.inspectorRepository.increment(
          { id: inspectorId },
          'completedInspections',
          1,
        );
      }
    }

    return {
      message: 'Inspection completed successfully',
      order: await this.findOne(id, user),
    };
  }

  async cancelOrder(id: number, reason: string, user: any) {
    const order = await this.findOne(id, user);

    if ([OrderStatus.COMPLETED, OrderStatus.CANCELLED].includes(order.status)) {
      throw new BadRequestException('Cannot cancel completed or already cancelled orders');
    }

    // Free up schedule if order was scheduled
    if (order.status === OrderStatus.SCHEDULED) {
      await this.scheduleRepository.update(
        { inspectionOrderId: id },
        { inspectionOrderId: null, available: true },
      );
    }

    await this.orderRepository.update(id, {
      status: OrderStatus.CANCELLED,
      cancellationReason: reason,
      cancelledAt: new Date(),
    });

    return {
      message: 'Order cancelled successfully',
      order: await this.findOne(id, user),
    };
  }

  async getOrderStats() {
    const totalOrders = await this.orderRepository.count();
    const ordersByStatus = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('order.status')
      .getRawMany();

    const recentOrders = await this.orderRepository.find({
      take: 10,
      order: { createdAt: 'DESC' },
      relations: ['property', 'inspector'],
    });

    return {
      totalOrders,
      ordersByStatus,
      recentOrders,
    };
  }

  async getClientOrders(clientId: number, query: any) {
    return this.findAll({ ...query, clientId }, { role: 'client', userId: clientId });
  }

  // New method to get orders for multiple clients
  async getOrdersForClients(clientIds: number[], query: any) {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    // Include relations
    queryBuilder.leftJoinAndSelect('order.property', 'property');
    queryBuilder.leftJoinAndSelect('order.orderClients', 'orderClients');
    queryBuilder.leftJoinAndSelect('orderClients.client', 'client');
    queryBuilder.leftJoinAndSelect('order.orderInspectors', 'orderInspectors');
    queryBuilder.leftJoinAndSelect('orderInspectors.inspector', 'inspector');

    // Filter by client IDs
    queryBuilder.where('orderClients.clientId IN (:...clientIds)', { clientIds });

    // Apply other filters from query
    if (query.status) {
      queryBuilder.andWhere('order.status = :status', { status: query.status });
    }

    if (query.search) {
      queryBuilder.andWhere(
        '(order.orderNumber ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Apply pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [orders, total] = await queryBuilder.getManyAndCount();

    return {
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getInspectorOrders(inspectorId: number, query: any) {
    return this.findAll({ ...query, inspectorId }, { role: 'inspector', userId: inspectorId });
  }

  // Helper method to get orders by inspector using the new junction table
  async getOrdersByInspector(inspectorId: number) {
    return this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.property', 'property')
      .leftJoinAndSelect('order.orderInspectors', 'orderInspectors')
      .leftJoinAndSelect('orderInspectors.inspector', 'inspector')
      .where('orderInspectors.inspectorId = :inspectorId AND orderInspectors.isActive = true', { inspectorId })
      .getMany();
  }

  // Client management methods
  async addClientToOrder(orderId: number, clientId: number, user: any) {
    // Verify order exists and user has permission
    await this.findOne(orderId, user);

    // Use the new service to add client
    await this.orderClientService.addClientToOrder(orderId, clientId);

    return {
      message: 'Client added to order successfully',
      order: await this.findOne(orderId, user),
    };
  }

  async removeClientFromOrder(orderId: number, clientId: number, user: any) {
    // Verify order exists and user has permission
    await this.findOne(orderId, user);

    // Use the new service to remove client
    await this.orderClientService.removeClientFromOrder(orderId, clientId);

    return {
      message: 'Client removed from order successfully',
      order: await this.findOne(orderId, user),
    };
  }

  async updateOrderClients(orderId: number, clientIds: number[], user: any) {
    // Verify order exists and user has permission
    await this.findOne(orderId, user);

    // Use the new service to replace all clients
    await this.orderClientService.replaceOrderClients(orderId, clientIds);

    return {
      message: 'Order clients updated successfully',
      order: await this.findOne(orderId, user),
    };
  }

  private async generateOrderNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get count of orders this month
    const startOfMonth = new Date(year, new Date().getMonth(), 1);
    const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);
    
    const monthlyCount = await this.orderRepository.count({
      where: {
        createdAt: Between(startOfMonth, endOfMonth),
      },
    });

    const sequence = String(monthlyCount + 1).padStart(4, '0');
    return `ORD-${year}${month}-${sequence}`;
  }

  private validateStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus) {
    const validTransitions = {
      [OrderStatus.PENDING]: [OrderStatus.SCHEDULED, OrderStatus.CANCELLED],//todo: sgould validate again
      [OrderStatus.SCHEDULED]: [OrderStatus.SCHEDULED, OrderStatus.CANCELLED],//todo: validate again
      [OrderStatus.IN_PROGRESS]: [OrderStatus.COMPLETED, OrderStatus.CANCELLED],//todo: validate again
      [OrderStatus.COMPLETED]: [], // No transitions from completed
      [OrderStatus.CANCELLED]: [], // No transitions from cancelled
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}`,
      );
    }
  }
}
